import axios from 'axios';
import { NavigateFunction } from 'react-router-dom';

let navigate: NavigateFunction;

export const setNavigate = (nav: NavigateFunction) => {
    navigate = nav;
};

const api = axios.create({
    baseURL: import.meta.env.VITE_API_URL_BACKEND,
});

api.interceptors.request.use((config) => {
    const token = localStorage.getItem('TOKEN');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    config.headers['Accept-Language'] = localStorage.getItem('language') || 'fr';
    return config;
});

api.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            localStorage.removeItem('TOKEN');
            if (navigate) {
                navigate('/');
            }
        }
        if (error.response?.status === 403) {
            if (navigate) {
                navigate('/unauthorized');
            }
        }
        return Promise.reject(error);
    }
);



export default api;
