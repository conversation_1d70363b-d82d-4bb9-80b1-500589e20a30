{"messages": {"success": "Opération effectuée avec succès !", "error": "Une erreur s'est produite", "loading": "Chargement...", "notFound": "Non trouvé", "not_found": "Utilisateur non trouvé dans le système des affaires sociales", "clientNotFound": "Client non trouvé", "found": "Utilisateur trouvé avec bon de commande disponible", "no_purchase_order": "Utilisateur trouvé mais aucun bon de commande disponible pour son gouvernorat", "undefined": "Remplir le (s) champs obligatoire (s)", "Trip_already_exists": "<PERSON><PERSON><PERSON> existant", "Failed_delete_trip": "Impossible de supprimer un trajet utilisé", "not_available_sale_period": "Aucune période de vente ouverte pour cet abonnement", "paidSubscriptionEditWarning": "Attention: La modification d'un abonnement payé entraînera l'annulation de la transaction existante et la création d'une nouvelle transaction."}, "permissions": {"Roles Management": "Gestion de sécurité", "manage_roles_permissions": "g<PERSON><PERSON> permissions rôles", "view_roles_permissions": "voir permissions rôles", "add_roles_permissions": "ajouter permissions rôles", "update_roles_permissions": "modifier permissions rôles", "delete_roles_permissions": "supprimer permissions rôles", "Admins Management": "Gestion des administrateurs", "manage_admins": "gérer administrateurs", "view_admins": "voir administrateurs", "add_admins": "ajouter administrateurs", "update_admins": "modifier administrateurs", "delete_admins": "supprimer administrateurs", "Clients Management": "Gestion des abonnés", "manage_clients": "<PERSON><PERSON><PERSON> a<PERSON>", "view_clients": "voir abonnés", "add_clients": "ajouter abonn<PERSON>", "update_clients": "modifier abonnés", "delete_clients": "supprimer abonn<PERSON>", "Delegations Management": "Gestion des délégations", "manage_delegations": "gérer délégations", "view_delegations": "voir délégations", "add_delegations": "ajouter délégations", "update_delegations": "modifier délégations", "delete_delegations": "supprimer délégations", "Stations Management": "Gestion des administrateurs", "manage_stations": "gérer stations", "view_stations": "voir stations", "add_stations": "ajouter stations", "update_stations": "modifier stations", "delete_stations": "supprimer stations", "Routes Management": "Gestion des trajets", "manage_routes": "g<PERSON><PERSON> trajets", "view_routes": "voir trajets", "add_routes": "ajouter trajets", "update_routes": "modifier trajets", "delete_routes": "supprimer trajets", "Establishments Management": "Gestion des établissements", "manage_establishments": "g<PERSON>rer établissements", "view_establishments": "voir établissements", "add_establishments": "ajouter établissements", "update_establishments": "modifier établissements", "delete_establishments": "supprimer établissements", "ABN Types Management": "Gestion des types abn", "manage_abnTypes": "gérer types abn", "view_abnTypes": "voir types abn", "add_abnTypes": "ajouter types abn", "update_abnTypes": "modifier types abn", "delete_abnTypes": "supprimer types abn", "ABN SubTypes Management": "Gestion des types abn", "manage_abnSubTypes": "gérer sous types abn", "view_abnSubTypes": "voir sous types abn", "add_abnSubTypes": "ajouter sous types abn", "update_abnSubTypes": "modifier sous types abn", "delete_abnSubTypes": "supprimer sous types abn", "Base Tariffs Management": "Gestion des tarifs base", "manage_baseTariff": "gérer tarifs base", "view_baseTariff": "voir tarifs base", "add_baseTariff": "ajouter tarifs base", "update_baseTariff": "abn tarifs base", "delete_baseTariff": "supprimer tarifs base"}, "dashboard": {"Statistiques_A1": "Statistiques A1", "Statistiques_A2": "Statistiques A2", "active_plans": "Plans actifs", "expiring": "Expirant <PERSON><PERSON><PERSON><PERSON>", "revenue": "<PERSON><PERSON><PERSON>", "total_subscribers": "Total des abonnés"}, "login": {"welcome": "Bienvenue !", "subtitle": "Connectez-vous à votre compte", "tooltip": "Choisissez votre méthode de connexion préférée", "email": "E-mail", "phone": "Téléphone", "cin": "Num CIN", "password": "Mot de passe", "signIn": "Se connecter", "forgotPassword": "Mot de passe oublié ?", "or": "OU", "noAccount": "Vous n'avez pas de compte ?", "signUp": "Ins<PERSON>rivez-vous maintenant", "validation": {"required": "Ce champ est obligatoire", "email": "Veuillez entrer une adresse e-mail valide", "phone": "Le numéro de téléphone doit contenir 8 chiffres", "cin": "Le CIN doit contenir 8 chiffres"}, "placeholder": {"email": "Entrez votre e-mail", "phone": "Entrez votre numéro de téléphone", "cin": "Entrez votre CIN", "password": "Entrez votre mot de passe", "captcha": "<PERSON><PERSON><PERSON> le captcha"}}, "register": {"welcome": "Bienvenue !", "step1Title": "Informations personnelles", "step2Title": "Vérification du téléphone", "step3Title": "<PERSON><PERSON><PERSON>", "step4Title": "<PERSON>é<PERSON> du compte", "step5Title": "Vérification de l'email", "step6Title": "Termes et Conditions", "subtitle": "C<PERSON>ez un compte pour commencer.", "tooltip": "Saisissez vos informations avec soin.", "placeholder": {"email": "Adresse e-mail", "password": "Mot de passe", "confirmPassword": "Confirmez le mot de passe", "nom": "Nom", "prenom": "Prénom", "phone": "Numéro de téléphone", "adresse": "<PERSON><PERSON><PERSON>", "cin": "CIN"}, "validation": {"required": "Ce champ est obligatoire.", "email": "Veuillez entrer une adresse e-mail valide", "phone": "Le numéro de téléphone doit contenir 8 chiffres", "cin": "Le CIN doit contenir 8 chiffres", "passwordMismatch": "Les mots de passe ne correspondent pas.", "acceptTerms": "<PERSON><PERSON> de<PERSON> accepter les termes et conditions.", "passwordLength": "Le mot de passe doit contenir au moins 8 caractères.", "passwordComplexity": "Le mot de passe doit contenir au moins une lettre majuscule, une lettre minuscule, un chiffre et un caractère special."}, "acceptTerms": "J'accepte les", "termsLink": "termes et conditions", "next": "Suivant", "previous": "Précédent", "signUp": "S'inscrire", "or": "ou", "haveAccount": "Vous avez déjà un compte?", "signIn": "Se connecter", "resendCode": "Renvoi du code", "phoneValidated": "Numéro de téléphone validé", "emailValidated": "E-mail validé", "retryIn": "<PERSON><PERSON><PERSON><PERSON> dans", "seconds": "secondes", "OTPPhoneTitle": "Entrez le code OTP", "OTPPhoneDescription": "Veuillez entrer le code OTP envoyé à votre numéro de téléphone", "OTPEmailTitle": "Entrez le code OTP", "OTPEmailDescription": "Veuillez entrer le code OTP envoyé à votre adresse e-mail"}, "reset": {"step1Title": "Saisissez votre e-mail", "step2Title": "Entrez le code", "step3Title": "Réinitialisez votre mot de passe", "requestTitle": "Réinitialisation du mot de passe", "requestDescription": "Saisissez les informations nécessaires pour réinitialiser votre mot de passe.", "emailPlaceholder": "Adresse e-mail", "newPasswordTitle": "Nouveau mot de passe", "newPasswordDescription": "Veuillez entrer et confirmer votre nouveau mot de passe.", "passwordPlaceholder": "Nouveau mot de passe", "confirmPlaceholder": "Confirmez le mot de passe", "resendCode": "Renvoyer le code", "retryIn": "<PERSON><PERSON><PERSON><PERSON> dans", "seconds": "secondes", "haveAccount": "Vous avez déjà un compte ?", "signIn": "Se connecter", "next": "Suivant", "previous": "Précédent", "confirm": "Confirmer", "validation": {"emailRequired": "Veuillez saisir votre adresse e-mail.", "emailValid": "<PERSON><PERSON><PERSON>z entrer une adresse e-mail valide.", "invalidOtp": "Code OTP invalide. Veuillez réessayer.", "passwordRequired": "Le mot de passe est requis.", "passwordLength": "Le mot de passe doit comporter au moins 8 caractères.", "confirmRequired": "Veuillez confirmer votre mot de passe.", "passwordMatch": "Les mots de passe ne correspondent pas.", "passwordComplexity": "Le mot de passe doit contenir au moins une lettre majuscule, une lettre minuscule, un chiffre et un caractère special."}, "EmailValidated": "E-mail validé avec succès !", "OTPEmailTitle": "Entrez le code de confirmation", "OTPEmailDescription": "Nous avons envoyé un code à votre email. Veuillez le saisir ci-dessous pour vérifier votre email."}, "access_denied": {"title": "<PERSON><PERSON><PERSON>", "sub_title": "<PERSON><PERSON><PERSON><PERSON>, vous n'avez pas l'autorisation d'accéder à cette page.", "button": "Retour à l'accueil"}, "not_found": {"title": "Page Introuvable", "sub_title": "<PERSON><PERSON><PERSON><PERSON>, la page que vous avez visitée n'existe pas.", "button": "Retour à l'accueil"}, "auth_header": {"profile": "Profil", "settings": "Paramètres", "logout": "Déconnexion"}, "auth_sidebar": {"dashboard": "Tableau de bord", "categories": {"security": "Sécurité", "configs": "Paramètrages", "subscriptions": "Abonnements", "clients": "Clients"}, "roles_permissions": "Rôles et permissions", "manage_admins": "Administrateurs", "manage_civil_clients": "Abonnés civils", "manage_school_clients": "Abonnés scolaires", "manage-governorates": "Gouvernorats", "manage_delegations": "Délegations", "manage_stations": "Gestion des Stations", "manage_routes": "Gestion des Trajets", "manage_lines": "Gestion des Lignes", "manage_establishmentTypes": "Types établissements", "manage_establishment": "Etablissements", "manage_abnTypes": "Types abonnements", "manage_baseTariff": "Bases tarifaires", "manage_campaignTypes": "Types campagnes", "manage_campaigns": "Campagnes", "manage_salesPeriods": "Periodes ventes", "manage_civilSalesPeriods": "Periodes ventes civile", "manage_salesPoints": "Points de ventes", "manage_assignAgents": "Affectation des agents", "manage_schoolDegrees": "Niveaux scolaires", "manage_abnPeriods": "Periodes abonnements", "manage_civilSubscriptions": "Abonnements civils", "manage_subscriptions": "Abonnements", "new_subscription": "Nouvel abonnement", "my_subscriptions": "Mes abonnements"}, "manage_profile": {"welcome": "Bienvenue", "manage_your_informations": "<PERSON><PERSON><PERSON> vos informations personnelles en toute sécurité", "personal_informations": "Informations personnelles", "firstname": "Prénom", "lastname": "Nom de famille", "email": "E-mail", "phone": "Téléphone", "cin": "CIN", "address": "<PERSON><PERSON><PERSON>", "old_password": "Mot de passe actuel", "new_password": "Nouveau mot de passe", "confirm_password": "Confirmer le mot de passe", "update": "Mettre à jour", "update_email": "Mettre à jour l'adresse email", "change_password": "Changer le mot de passe", "requestTitle": "Mettre à jour votre adresse e-mail", "requestDescription": "Saisissez votre adresse e-mail actuelle, et nous vous enverrons un code pour la réinitialiser.", "oldEmailPlaceholder": "Adresse e-mail actuelle", "newEmailTitle": "Nouvelle adresse E-mail", "newEmailDescription": "Veuillez entrer votre nouvelle adresse e-mail, et nous vous enverrons un code pour la vérifier.", "newEmailPlaceholder": "Nouvelle adresse e-mail", "confirmPlaceholder": "Confirmez le mot de passe", "resendCode": "Renvoyer le code", "retryIn": "<PERSON><PERSON><PERSON><PERSON> dans", "seconds": "secondes", "haveAccount": "Vous avez déjà un compte ?", "signIn": "Se connecter", "next": "Suivant", "previous": "Précédent", "confirm": "Confirmer", "validation": {"emailRequired": "Veuillez saisir votre adresse e-mail.", "emailValid": "<PERSON><PERSON><PERSON>z entrer une adresse e-mail valide.", "invalidOtp": "Code OTP invalide. Veuillez réessayer.", "passwordRequired": "Le mot de passe est requis.", "passwordLength": "Le mot de passe doit comporter au moins 8 caractères.", "confirmRequired": "Veuillez confirmer votre mot de passe.", "passwordMatch": "Les mots de passe ne correspondent pas.", "passwordComplexity": "Le mot de passe doit contenir au moins une lettre majuscule, une lettre minuscule, un chiffre et un caractère special."}, "EmailValidated": "E-mail validé avec succès !", "OTPEmailTitle": "Entrez le code de confirmation", "OTPEmailDescription": "Nous avons envoyé un code à votre email. Veuillez le saisir ci-dessous pour vérifier votre email.", "NewEmailValidated": "Nouveau E-mail validé avec succès !", "OTPNewEmailTitle": "Entrez le code de confirmation", "OTPNewEmailDescription": "Nous avons envoyé un code à votre nouvelle adresse email. Veuillez le saisir ci-dessous pour vérifier votre email."}, "subscriptions": {"clickToSubscribe": "Cliquez pour s'abonner", "search": "<PERSON><PERSON><PERSON>", "state": "Etat", "type": "Type", "results": "Résultats", "reset": "Réinitialiser", "upcoming": "À venir", "available": "Disponible", "bought": "<PERSON><PERSON><PERSON><PERSON>", "in": "dans", "days": "jours", "newSubscriptions": "Nouveaux abonnements", "not_available": "Indisponible", "lastSubscription": "Dernier abonnement", "payNow": "Payer maintenant", "noPhoto": "Pas de photo", "title": "Abonnements civil", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "next": "Suivant", "confirm": "Confirmer", "transportCard": "Carte de transport", "validityInfo": "Vous devez aller au guichet pour recupérer votre carte de transport.", "validation": {"required": "Ce champ est obligatoire", "cin": "Le CIN doit contenir 8 chiffres", "userNotFound": "Utilisateur non trouvé, Veuillez vérifier votre identifiant ou votre date de naissance"}, "placeholders": {"cin": "Entrez votre CIN", "birthdate": "Entrez votre date de naissance", "grade_level": "Entrez le niveau scolaire", "school_year": "Ex: 2024-2025", "period": "Sélectionnez une période", "housing_governorate": "Sélectionnez une gouvernorat", "housing_delegation": "Sélectionnez une délégation", "station_depart": "Sélectionnez une station de départ", "station_arrival": "Sélectionnez une station d'arrivée", "line": "Sélectionnez une ligne", "affaire_sociale_id": "Entrez l'identifiant"}, "labels": {"cin": "CIN", "parent_cin": "CIN du parent", "unique_student_ID": "Identifiant unique de l’élève", "birthdate": "Date de naissance", "lastname": "Nom", "firstname": "Prénom", "gender": "Genre", "delegation_of_establishment": "Délégation de l'établissement", "establishment": "Établissement", "grade_level": "Niveau scolaire", "school_year": "Année scolaire", "period": "Période", "semester1": "Semestre 1", "semester2": "Semestre 2", "annual": "<PERSON><PERSON>", "housing_governorate": "Gouvernorat de logement", "housing_delegation": "Délégation de logement", "station_depart": "Station de départ", "station_arrival": "Station d'arrivée", "line": "Ligne", "hasVacation": "Vacances", "isSocialAffairs": "Affaires sociales", "affaire_sociale_id": "Identifiant Affaires Sociales"}, "options": {"male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Fé<PERSON>n", "withVacation": "Avec vacances", "withoutVacation": "Sans vacances", "withSocialAffairs": "Avec Affaires Sociales", "withoutSocialAffairs": "Sans Affaires Sociales"}, "priceGlobal": "Montant global", "currency": "TND", "uploadPhoto": "Upload Photo", "cropImage": "<PERSON><PERSON><PERSON> l'image", "confirmCrop": "Confirm<PERSON> le rognage", "cancel": "Annuler"}, "manage_newSubs": {"title": "Abonnements", "add": "Ajouter un abonnement", "edit": "Modifier l'abonnement", "details": "Détails de l'abonnement", "detailsAndPayment": "Détails et Paiement", "selectSubscriptionType": "Sélectionnez le type d'abonnement", "save": "Enregistrer", "createSuccess": "Abonnement créé avec succès !", "updateSuccess": "Abonnement mis à jour avec succès !", "renewalSuccess": "Abonnement renouvelé avec succès !", "deleteSuccess": "Abonnement supprimé avec succès !", "upload": "Télécharger une photo", "cropImage": "Recadrer la photo", "noPhoto": "Aucune photo", "backgroundRequirement": "L'image doit avoir un fond blanc", "selectSubscriptionPrompt": "Veuillez sélectionner un type d'abonnement", "selectDepartureFirst": "Veuillez d'abord sélectionner une station de départ", "confirmCrop": "Enregistrer", "confirmAction": "Confirmer l'action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter cet abonnement ?", "confirmUpdate": "Êtes-vous sûr de vouloir modifier cet abonnement ?", "confirmPaymentTitle": "Confirmer le paiement", "confirmPaymentMessage": "Êtes-vous sûr de vouloir confirmer ce paiement ?", "addClient": "Nouvel abonné", "cancel": "Annuler", "clientInfo": "Informations du client", "clientInformation": "Informations Client", "confirmPayment": "Confirmer le paiement", "subscriptionInfo": "Informations de l'abonnement", "subscriptionDetails": "Détails de l'Abonnement", "searchStudent": "Rechercher un élève", "searchStudentCIN": "Rechercher un étudiant", "searchClient": "Rechercher un client", "search": "<PERSON><PERSON><PERSON>", "searchHint": "Rechercher par vos coordonées", "notPaid": "Non payé", "alreadyPaid": "<PERSON><PERSON>", "openReceipt": "Imprimer les reçus", "showSubsDetails": "Voir les détails de l'abonnement", "days": "Jours", "showCard": "Voir la carte d'abonnement", "renewal": "Renouvellement d'abonnement", "withVacations": "Avec vacances", "withoutVacations": "Sans vacances", "confirmRenewal": "Êtes-vous sûr de vouloir renouveler cet abonnement ?", "modal": {"cardTitle": "Details de la carte d'abonnement", "print": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>"}, "card": {"title": "Details de la carte d'abonnement", "name": "Nom", "number": "Numéro de carte", "expiry": "Date d'expiration", "footerText": "Merci d'être un membre précieux !"}, "restDays": {"1": "<PERSON><PERSON>", "2": "<PERSON><PERSON>", "3": "<PERSON><PERSON><PERSON><PERSON>", "4": "<PERSON><PERSON>", "5": "<PERSON><PERSON><PERSON><PERSON>", "6": "<PERSON><PERSON>", "7": "<PERSON><PERSON><PERSON>"}, "paymentOptions": {"notPayed": "Non Payé", "payed": "<PERSON><PERSON>", "expired": "Expiré", "pending": "En attente", "canceled": "<PERSON><PERSON><PERSON>"}, "options": {"withVacation": "Avec vacances", "withoutVacation": "Sans vacances"}, "tooltips": {"payment": "<PERSON><PERSON><PERSON> le paiement", "renew": "Renouveler l'abonnement", "viewCard": "Voir la carte d'abonnement"}, "filters": {"client": "Rechercher par identifiant (abonné)"}, "labels": {"id": "Id", "restDays": "Nombre de jours de repos", "socialAffair": "Affaire sociale", "isStagiaire": "Stagiaire", "parentCin": "CIN du parent", "stageStartDate": "Date de début de stage", "stageEndDate": "Date de fin de stage", "specialClient": "Type de client spécial", "civil": "Civil", "universitaire": "Universitaire", "scolaire": "Scolaire", "subscriptionType": "Type d'abonnement", "special": "Client special", "route": "<PERSON><PERSON><PERSON>", "socialAffairYes": "Affaire sociale", "socialAffairNo": "Ré<PERSON>lier", "periodicity": "Périodicité", "subsNumber": "Nombre des abonnés", "governorate_depart": "Gouvernorat <PERSON>", "governorate_arrival": "Gouvernorat d'arrivée", "delegation_depart": "Délégation de départ", "station_depart": "Station de départ", "delegation_arrival": "Délégation d'arrivée", "station_arrival": "Station d'arrivée", "image": "Image", "createdAt": "<PERSON><PERSON><PERSON> le", "clientCin": "Abonné par cin", "client": "<PERSON><PERSON><PERSON><PERSON>", "clientName": "Nom du client", "clientUniversitaire": "Abonné universitaire", "clientCivil": "Abonné civil", "clientImpersonal": "<PERSON><PERSON><PERSON><PERSON> impersonnel", "clientConventional": "Abonné conventionnel", "identityNumber": "N° Identifiant", "conventionNumber": "N° Convention", "cin": "CIN", "matricule": "<PERSON><PERSON>", "socialAffairId": "Identifiant Affaires Sociales", "establishment": "Établissement", "schoolDegree": "Niveau scolaire", "dob": "Date de naissance", "phone": "Téléphone", "stagiaire": "Stagiaire", "matriculate": "<PERSON><PERSON>", "clientDob": "Date de naissance", "line": "Ligne", "vacation": "Vacances", "totalAmount": "Montant total", "status": "Statut", "reversed": "Inversé", "reversedYes": "Inversé", "reversedNo": "Normal", "abnType": "Abonnement", "trip": "<PERSON><PERSON><PERSON>", "actions": "Actions", "startDate": "Date de début", "endDate": "Date de fin", "amount": "<PERSON><PERSON>"}, "errors": {"clientRequired": "Le nombre des abonnés est requis", "matriculateRequired": "L'id élève est requise", "clientDobRequired": "La date de naissance est requise", "subsNumberRequired": "L'abonné est requis", "restDaysRequired": "Le nombre de jours de repos est requis", "photoRequired": "La photo est requise", "periodicityRequired": "La périodicité est requise", "lineRequired": "Sé<PERSON><PERSON>ner une ligne", "vacationRequired": "Veuillez indiquer si l'abonnement inclut les vacances.", "governorateRequired": "Le gouvernorat est requis.", "delegationDepartRequired": "La délégation de départ est requise", "delegationArrivalRequired": "La délégation d'arrivée est requise", "stationDepartRequired": "La station de départ est requise", "parentCinRequired": "Le CIN du parent est requis", "stationArrivalRequired": "La station d'arrivée est requise", "socialAffairRequired": "L'Affaire sociale est requise", "statusRequired": "Le statut est requis", "abnTypeRequired": "Le type d'abonnement est requis", "unexpected": "Une erreur inattendue s'est produite", "cinRequired": "Le cin est requis", "idEleveRequired": "L'id élève est requis", "dobRequired": "La date de naissance est requise", "isStagiaRequired": "Le champ Stagiare est obligatoire", "socialAffairIdRequired": "L'identifiant des affaires sociales est requis", "stageStartDateRequired": "La date de début de stage est requise", "stageEndDateRequired": "La date de fin de stage est requise", "specialClientRequired": "Le type de client spécial est requis", "tooManyRestDays": "Trop de jours de repos sélectionnés. Maximum {{max}} jours pour {{periodicity}}"}, "messages": {"socialAffairIdInvalid": "Vous n’êtes pas éligible à une aide sociale.", "socialAffairIdValid": "Vous êtes éligible à une aide sociale.", "found": "Trouvé avec succès!", "not_found": "Non trouvé", "no_purchase_order": "Aucun bon de commande disponible"}, "placeholders": {"selectClientCin": "Sélectionner un abonné par cin", "selectClient": "Sélectionner un abonné", "matriculate": "Entrer id élève", "clientDob": "Entrer date de naissance", "parentCin": "Entrer CIN du parent", "subsNumber": "Entrer le nombre des abonnés", "restDays": "Sélectionner le nombre de jour de repos", "startDate": "Date de début", "endDate": "Date de fin", "periodicity": "Sélectionner une périodicité", "abnType": "Sélectionnez le type d'abonnement", "line": "Sé<PERSON><PERSON>ner une ligne", "delegation": "Sélectionnez une délégation", "governorate": "Sélectionnez un gouvernorat", "station_depart": "Sélectionnez la station de départ", "station_arrival": "Sélectionnez la station d'arrivée", "socialAffair": "Sélectionnez l'affaire sociale", "socialAffairId": "Entrez l'identifiant des affaires sociales", "idEleve": "Entrer id élève", "cin": "Entrer cin", "stageStartDate": "Entrez la date de début de stage", "stageEndDate": "Entrez la date de fin de stage", "specialClient": "Sélectionnez le type de client spécial"}, "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet abonnement ?", "yes": "O<PERSON>", "no": "Non", "close": "<PERSON><PERSON><PERSON>", "selectStationsFirst": "Veuillez d'abord sélectionner les stations de départ et d'arrivée"}, "manage_users": {"labels": {"lastname": "Nom", "firstname": "Prénom", "matricule": "Matricule", "cin": "CIN", "address": "<PERSON><PERSON><PERSON>", "phone": "Téléphone", "dob": "Date de naissance", "governorate": "Gouvernorat", "delegation": "Délégation", "establishment": "Établissement", "schoolDegree": "Niveau", "univDegree": "Niveau universitaire", "degree": "Niveau"}, "errors": {"lastnameRequired": "Le nom est requis", "firstnameRequired": "Le prénom est requis", "cinRequired": "Le CIN est requis", "addressRequired": "L'adresse est requise", "phoneRequired": "Le numéro de téléphone est requis", "phoneInvalid": "Le numéro de téléphone doit contenir 8 chiffres", "dobRequired": "La date de naissance est requise", "governorateRequired": "Le gouvernorat est requis", "delegationRequired": "La délégation est requise", "establishmentRequired": "L'établissement est requis", "schoolDegreeRequired": "Le niveau scolaire est requis", "noDegreeForAge": "Aucun niveau scolaire disponible pour cet âge"}, "placeholders": {"governorate": "Sélectionnez un gouvernorat", "delegation": "Sélectionnez une délégation", "dob": "Sélectionnez une date de naissance"}, "client": {"placeholders": {"lastname": "Entrez le nom", "firstname": "Entrez le prénom", "address": "Entrez l'adresse", "phone": "Entrez le numéro de téléphone", "cin": "Entrez le CIN", "matricule": "Entrez la matricule", "establishment": "Sélectionnez un établissement", "schoolDegree": "Sélectionnez un niveau scolaire"}}, "selectGovernorate": "Veuillez d'abord sélectionner un gouvernorat"}, "common": {"km": "Km", "schedules": "<PERSON><PERSON><PERSON>", "tnd": "TND", "verify": "Vérifier", "verifying": "Vérification...", "close": "<PERSON><PERSON><PERSON>", "print": "<PERSON><PERSON><PERSON><PERSON>", "loading": "Chargement...", "noData": "<PERSON><PERSON><PERSON> don<PERSON>", "errors": {"unexpected": "Une erreur inattendue s'est produite"}, "yes": "O<PERSON>", "no": "Non"}}