import React, { useState } from "react";
import {
  Form,
  Input,
  Button,
  Card,
  Tooltip,
  Steps,
  Divider,
  Checkbox,
} from "antd";
import { Lock, Phone, Home, IdCard, HelpCircle, User } from "lucide-react";
import { useTranslation } from "react-i18next";
import { assets } from "../../assets/assets.ts";
import { Link, useNavigate } from "react-router-dom";
import { OTPProps } from "antd/es/input/OTP";
import { useMediaQuery } from "react-responsive";
import { LanguageSelector } from "../../components";
import { useDispatch, useSelector } from "react-redux";
import {
  storeSubscriber,
  storeSubscriberStepFour,
  storeSubscriberStepOne,
  storeSubscriberStepThree,
  storeSubscriberStepTwo,
} from "../../features/auth/authSlice.ts";
import { toast, ToastContainer } from "react-toastify";

const { Step } = Steps;

const Register: React.FC = () => {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const dispatch: any = useDispatch();
  const { loading } = useSelector((state: any) => state.auth);
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [countdownEmail, setCountdownEmail] = useState(0);
  const [isEmailLinkActive, setIsEmailLinkActive] = useState(true);
  const [otpEmailValue, setOtpEmailValue] = useState("");
  const [otpEmailError, setOtpEmailError] = useState(false);
  const [isEmailValidated, setIsEmailValidated] = useState(false);
  const [isTermsChecked, setIsTermsChecked] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [allFormFields, setAllFormFields] = useState({});

  const isSmallScreen: any = useMediaQuery({ query: "(max-width: 575px)" });

  const onChangeOtpEmail: OTPProps["onChange"] = (text) => {
    setOtpEmailError(false);
    setOtpEmailValue(text); // Stocke l'OTP complet pour l'email
  };
  const handleTermsChange = (e: any) => {
    const checked = e.target.checked;
    setIsTermsChecked(checked);
    console.log(checked);
    if (checked) {
      setShowTooltip(false); // Cache le tooltip si l'utilisateur coche
    }
  };

  // Fonction pour renvoyer le code par email
  const handleResendEmailCode = () => {
    // Logique pour renvoyer le code par email
    setIsEmailLinkActive(false);
    setCountdownEmail(60); // Démarre un compte à rebours de 60 secondes
    // Logique d'envoi du code (par exemple, via une API)
    const interval = setInterval(() => {
      setCountdownEmail((prev: any) => {
        if (prev === 1) {
          clearInterval(interval);
          setIsEmailLinkActive(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    // handle step 3
    dispatch(storeSubscriberStepThree(allFormFields)).unwrap();
  };

  // List des steps
  const steps = [
    {
      title: t("register.step1Title"),
      content: (
        <>
          <Form.Item
            className="mb-3"
            hasFeedback
            name="lastname"
            rules={[
              { required: true, message: t("register.validation.required") },
            ]}
          >
            <Input
              prefix={<User size={18} className="text-gray-400" />}
              placeholder={t("register.placeholder.nom")}
              className="rounded-lg text-sm py-3"
            />
          </Form.Item>

          <Form.Item
            className="mb-3"
            hasFeedback
            name="firstname"
            rules={[
              { required: true, message: t("register.validation.required") },
            ]}
          >
            <Input
              prefix={<User size={18} className="text-gray-400" />}
              placeholder={t("register.placeholder.prenom")}
              className="rounded-lg text-sm py-3"
            />
          </Form.Item>

          <Form.Item
            hasFeedback
            name="phone"
            rules={[
              { required: true, message: t("register.validation.required") },
              {
                pattern: /^[0-9]{8}$/,
                message: t("register.validation.phone"),
              },
            ]}
          >
            <Input
              prefix={<Phone size={18} className="text-gray-400" />}
              type="number"
              placeholder={t("register.placeholder.phone")}
              className="rounded-lg text-sm py-3"
            />
          </Form.Item>
        </>
      ),
    },
    {
      title: t("register.step3Title"),
      content: (
        <>
          <Form.Item
            className="mb-3"
            hasFeedback
            name="address"
            rules={[
              { required: true, message: t("register.validation.required") },
            ]}
          >
            <Input
              prefix={<Home size={18} className="text-gray-400" />}
              placeholder={t("register.placeholder.adresse")}
              className="rounded-lg text-sm py-3"
            />
          </Form.Item>

          <Form.Item
            className="mb-3"
            hasFeedback
            name="cin"
            rules={[
              { required: true, message: t("register.validation.required") },
              { pattern: /^[0-9]{8}$/, message: t("register.validation.cin") },
            ]}
          >
            <Input
              prefix={<IdCard size={18} className="text-gray-400" />}
              placeholder={t("register.placeholder.cin")}
              className="rounded-lg text-sm py-3"
            />
          </Form.Item>
        </>
      ),
    },
    {
      title: t("register.step4Title"),
      content: (
        <>
          <Form.Item
            className="mb-3"
            hasFeedback
            name="email"
            rules={[
              { required: true, message: t("login.validation.required") },
              { type: "email" as const, message: t("login.validation.email") },
            ]}
          >
            <Input
              type="email"
              placeholder={t(`login.placeholder.email`)}
              className="text-sm py-3"
            />
          </Form.Item>

          <Form.Item
            className="mb-3"
            name="password"
            rules={[
              { required: true, message: t("register.validation.required") },
              { min: 8, message: t("register.validation.passwordLength") },
              {
                pattern:
                  /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[+-.!@\$%\^&\*()])/,
                message: t("register.validation.passwordComplexity"),
              },
            ]}
          >
            <Input.Password
              prefix={<Lock size={18} className="text-gray-400" />}
              placeholder={t("register.placeholder.password")}
              className="text-sm py-3"
            />
          </Form.Item>

          <Form.Item
            className="mb-3"
            name="confirmPassword"
            dependencies={["password"]}
            rules={[
              { required: true, message: t("register.validation.required") },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("password") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(
                    new Error(t("register.validation.passwordMismatch"))
                  );
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<Lock size={18} className="text-gray-400" />}
              placeholder={t("register.placeholder.confirmPassword")}
              className="text-sm py-3"
            />
          </Form.Item>
        </>
      ),
    },
    {
      title: t("register.step5Title"),
      content: (
        <>
          {!isEmailValidated && (
            <>
              <h2 className="text-2xl text-center font-bold text-gray-800 mb-2">
                {t("register.OTPEmailTitle")}
              </h2>
              <p className="text-gray-600 text-center">
                {t("register.OTPEmailDescription")}
              </p>
            </>
          )}
          <Form.Item validateStatus={otpEmailError ? "error" : ""}>
            <div className="lg:px-16 sm:px-3 py-6">
              {isEmailValidated ? (
                <div className="text-sm p-6 border-2 border-gray-100 rounded-lg shadow-sm">
                  <div className="flex justify-center items-center">
                    <img src={assets.validatedGif} alt="GIF" width={70} />
                  </div>
                  <div className="flex items-center justify-center text-red-600">
                    {t("register.emailValidated")}
                  </div>
                </div>
              ) : (
                <div className="mt-3 text-center justify-center items-center">
                  <Input.OTP
                    length={8}
                    size="middle"
                    className={`justify-center ${
                      otpEmailError ? "border-red-500" : ""
                    }`}
                    autoFocus
                    onChange={onChangeOtpEmail}
                  />

                  <div className="mt-3 flex text-center justify-center items-center">
                    <span>
                      {isEmailLinkActive ? (
                        <Button
                          type="link"
                          className="text-red-500 text-sm"
                          onClick={handleResendEmailCode}
                        >
                          Renvoi du code
                        </Button>
                      ) : (
                        <span>
                          {t("register.retryIn")}{" "}
                          <span className="font-semibold">
                            {countdownEmail}
                          </span>{" "}
                          {t("register.seconds")}
                        </span>
                      )}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </Form.Item>
        </>
      ),
    },
    {
      title: t("register.step6Title"),
      content: (
        <div className="text-center">
          <Form.Item>
            <Tooltip
              title={
                !isTermsChecked ? t("register.validation.acceptTerms") : ""
              }
              open={showTooltip && !isTermsChecked}
              placement="top"
            >
              <Checkbox onChange={handleTermsChange}>
                {t("register.acceptTerms")}{" "}
                <Link
                  className="text-red-500 hover:text-red-800"
                  to="/terms"
                  target="_blank"
                >
                  {t("register.termsLink")}
                </Link>
              </Checkbox>
            </Tooltip>
          </Form.Item>
        </div>
      ),
    },
  ];

  // Handle input errors messages
  const handleInputErrors: any = (error: any) => {
    const apiErrors = error.errors;
    // Transformez en tableau pour setFields
    const fields = Object.entries(apiErrors).map(([fieldName, messages]) => ({
      name: fieldName,
      errors: messages as string[],
    }));
    form.setFields(fields);
  };

  const next = () => {
    form
      .validateFields()
      .then(() => {
        switch (currentStep) {
          case 0:
            // handle step 1
            dispatch(storeSubscriberStepOne(form.getFieldsValue()))
              .unwrap()
              .then((response: any) => {
                setAllFormFields({
                  ...allFormFields,
                  ...form.getFieldsValue(),
                });
                setCurrentStep((prev: any) => prev + 1);
              })
              .catch((error: any) => {
                handleInputErrors(error);
                toast.error(error.message);
              });
            break;
          case 1:
            // handle step 2
            dispatch(storeSubscriberStepTwo(form.getFieldsValue()))
              .unwrap()
              .then((response: any) => {
                setAllFormFields({
                  ...allFormFields,
                  ...form.getFieldsValue(),
                });
                setCurrentStep((prev: any) => prev + 1);
              })
              .catch((error: any) => {
                handleInputErrors(error);
                toast.error(error.message);
              });
            break;
          case 2:
            // handle step 3
            dispatch(storeSubscriberStepThree(form.getFieldsValue()))
              .unwrap()
              .then((response: any) => {
                setAllFormFields({
                  ...allFormFields,
                  ...form.getFieldsValue(),
                });
                setCurrentStep((prev: any) => prev + 1);
              })
              .catch((error: any) => {
                handleInputErrors(error);
                toast.error(error.message);
              });
            break;
          case 3:
            // handle step 4
            dispatch(
              storeSubscriberStepFour({
                ...allFormFields,
                verificationCode: otpEmailValue,
              })
            )
              .unwrap()
              .then((response: any) => {
                setOtpEmailError(false);
                setIsEmailValidated(true);
                setAllFormFields({
                  ...allFormFields,
                  verificationCode: otpEmailValue,
                });
                setCurrentStep((prev: any) => prev + 1);
              })
              .catch((error: any) => {
                setOtpEmailError(true);
                toast.error(error.message);
              });
            break;
        }
      })
      .catch((errorInfo: any) => {
        console.log("Validation Failed:", errorInfo);
      });
  };

  const onFinish = () => {
    if (!isTermsChecked) {
      setShowTooltip(true);
    } else {
      if (isEmailValidated && isTermsChecked) {
        // handle store subscriber
        dispatch(storeSubscriber(allFormFields))
          .unwrap()
          .then((response: any) => {
            navigate("/");
          })
          .catch((error: any) => {
            toast.error(error.message);
          });
      }
    }
  };

  const prev = () => {
    setOtpEmailValue("");

    // Empêche de revenir à l'étape précédente si email validé
    if (currentStep === 0 || (currentStep === 3 && isEmailValidated)) {
      return;
    }
    setCurrentStep((prev: any) => prev - 1);
  };
  const isPrevDisabled =
    (currentStep === 3 && isEmailValidated) || currentStep === 0;

  return (
    <div className="min-h-screen flex">
      <ToastContainer />
      {/* Register Form */}
      <div
        className="
                    w-full
                    md:w-full
                    flex items-center justify-center
                    bg-center bg-no-repeat bg-contain
                    "
        style={{
          backgroundImage: `url(${assets.bg})`,
        }}
      >
        <Card
          className="w-full max-w-full  md:max-w-2xl lg:max-w-3xl m-5 !drop-shadow-sm py-20 lg:py-8"
          bordered={false}
        >
          {/* SELECT LANGUAGES */}
          <div
            className={`absolute top-5 ${
              i18n.language === "ar" ? "right-5" : "left-5"
            } `}
          >
            <LanguageSelector />
          </div>

          {currentStep === 0 && (
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-800 mb-2">
                {t("register.welcome")}
              </h2>
              <p className="text-gray-600 flex items-center justify-center gap-2">
                {t("register.subtitle")}
                <Tooltip title={t("register.tooltip")}>
                  <HelpCircle size={16} className="text-gray-400 cursor-help" />
                </Tooltip>
              </p>
            </div>
          )}

          <Steps current={currentStep} className="mb-8 mt-5">
            {steps.map((item, index) => (
              <Step key={index} title={isSmallScreen ? item.title : ""} />
            ))}
          </Steps>

          <Form
            form={form}
            name="register"
            initialValues={{ remember: true }}
            onFinish={onFinish}
            layout="vertical"
            size="large"
            onValuesChange={(changedValues) => {
              const fieldName = Object.keys(changedValues)[0];
              form.setFields([{ name: fieldName, errors: [] }]);
            }}
          >
            {steps[currentStep].content}

            <div className="flex justify-between">
              {currentStep > 0 && currentStep !== 4 && (
                <Button
                  disabled={isPrevDisabled || loading}
                  type="link"
                  onClick={prev}
                  className="h-12 text-sm text-black"
                >
                  {t("register.previous")}
                </Button>
              )}
              {currentStep === 0 ? (
                <Button
                  type="primary"
                  onClick={next}
                  className="h-12 text-sm w-full mx-auto block"
                  loading={loading}
                >
                  {t("register.next")}
                </Button>
              ) : currentStep < steps.length - 1 ? (
                <Button
                  type="link"
                  onClick={next}
                  className="h-12 text-sm text-red-500 !hover:text-red-500"
                  disabled={loading}
                >
                  {t("register.next")}
                </Button>
              ) : (
                <Button
                  type="primary"
                  onClick={onFinish}
                  className="h-12 text-sm w-full mx-auto block"
                  loading={loading}
                >
                  {t("register.signUp")}
                </Button>
              )}
            </div>

            <Divider className="border-gray-200">
              <span className="text-gray-400 text-sm">
                {t("login.or") || "OR"}
              </span>
            </Divider>
            <div className="text-gray-600 text-center mt-6">
              {t("register.haveAccount")}{" "}
              <Link
                to="/"
                className="mx-1 text-red-600 hover:text-red-500 hover:underline transition-colors font-medium"
              >
                {t("register.signIn")}
              </Link>
            </div>
          </Form>
        </Card>
      </div>

      {/* Right side - Image */}
      {/* <LandingSide/> */}
    </div>
  );
};

export default Register;
